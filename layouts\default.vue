<template>
  <v-app dark>
    <template v-if="!$slots.default">
      <header-component></header-component>

      <login-sidebar></login-sidebar>

      <v-main>
        <v-container fluid class="pt-0">
          <nuxt />
        </v-container>
      </v-main>

      <footer-component></footer-component>

      <cookie-popup v-if="isCookiePopupShown"></cookie-popup>

      <buzz-dialog
        v-if="isShownBuzzDialog"
        :dialog="isShownBuzzDialog"
        :item="buzzItem"
        @close="closeBuzzDialog"
      ></buzz-dialog>

      <lesson-evaluation-dialog
        :dialog="isShownLessonEvaluationDialog"
        :item="feedbackLessonData"
        @close="closeLessonEvaluationDialog"
      ></lesson-evaluation-dialog>
    </template>
    <template v-else>
      <slot></slot>
    </template>

    <snackbar :snackbar-options="snackbarOptions"></snackbar>
    <loader :is-loading="isLoading"></loader>
  </v-app>
</template>

<script>
import { isTouchDevice } from '~/helpers/check_device'
import Loader from '~/components/Loader'
import HeaderComponent from '~/components/Header'
import LoginSidebar from '~/components/LoginSidebar'
import FooterComponent from '~/components/Footer'
import BuzzDialog from '~/components/BuzzDialog'
import CookiePopup from '~/components/CookiePopup'
import LessonEvaluationDialog from '~/components/user-lessons/LessonEvaluationDialog'
import Snackbar from '~/components/Snackbar'

const localeDomains = require('~/locale-domains').default

export default {
  name: 'DefaultLayout',
  components: {
    HeaderComponent,
    LoginSidebar,
    FooterComponent,
    Loader,
    BuzzDialog,
    CookiePopup,
    LessonEvaluationDialog,
    Snackbar,
  },
  middleware({ store, route, i18n, redirect }) {
    if (store.getters['user/isUserLogged']) {
      const { uiLanguage } = store.state.user.item

      i18n.setLocaleCookie(uiLanguage)

      // For logged-in users, redirect to main domain without language prefix
      // since their language is determined by their settings
      const currentPath = route.path
      const isLanguagePrefixed = /^\/(pl|es)\//.test(currentPath)

      if (isLanguagePrefixed) {
        // Remove language prefix and redirect to main domain
        const pathWithoutLanguage = currentPath.replace(/^\/(pl|es)/, '') || '/'
        return redirect(
          process.env.NUXT_ENV_MAIN_DOMAIN_URL + pathWithoutLanguage
        )
      }
    }
  },
  data() {
    return {
      isShownBuzzDialog: false,
      buzzItem: {},
      feedbackLessonData: {},
      isShownLessonEvaluationDialog: false,
    }
  },
  head() {
    return {
      htmlAttrs: {
        lang: this.locale,
      },
      meta: [
        { lang: this.locale },
        { hid: 'og:image', property: 'og:image', content: this.previewImage },
        { hid: 'og:image:width', property: 'og:image:width', content: 1191 },
        { hid: 'og:image:height', property: 'og:image:height', content: 621 },
        {
          hid: 'og:image:type',
          property: 'og:image:type',
          content: 'image/png',
        },
      ],
      link: this.headLinks,
    }
  },
  computed: {
    locale() {
      return this.$i18n.locale
    },
    isLoading() {
      return !!this.$store.getters.isLoading
    },
    isCookiePopupShown() {
      return !this.$store.state.isCookieAllowed
    },
    previewImage() {
      return (
        process.env.NUXT_ENV_URL +
        require(`~/assets/images/preview-${this.locale}.png`)
      )
    },
    subdomain() {
      return this.$store.state.subdomain
    },
    isUserLogged() {
      return this.$store.getters['user/isUserLogged']
    },
    isStudent() {
      return this.$store.getters['user/isStudent']
    },
    headLinks() {
      const links = []
      const currentPath = this.$route.path !== '/' ? this.$route.path : ''

      // For canonical URL, use the current language's domain and path structure
      const currentLocale = localeDomains[this.locale] || localeDomains.en
      const canonicalPath =
        this.locale === 'en'
          ? currentPath
          : currentPath.replace(/^\/[a-z]{2}/, '')

      links.push({
        rel: 'canonical',
        href: 'https://' + currentLocale.domain + canonicalPath,
      })

      // Generate alternate language links
      Object.keys(localeDomains).forEach((language) => {
        const localeConfig = localeDomains[language]
        let alternatePath = currentPath

        // Remove current language prefix if present
        if (this.locale !== 'en' && currentPath.startsWith(`/${this.locale}`)) {
          alternatePath = currentPath.replace(`/${this.locale}`, '')
        }

        // Add target language prefix if not English
        if (language !== 'en' && localeConfig.pathPrefix) {
          alternatePath = localeConfig.pathPrefix + (alternatePath || '/')
        }

        links.push({
          rel: 'alternate',
          hreflang: language,
          href: 'https://' + localeConfig.domain + alternatePath,
        })
      })

      return links
    },
    snackbarOptions() {
      return this.$store.state.snackbar.item
    },
    lessonIdForFeedback() {
      return this.$store.getters['user/lessonIdForFeedback']
    },
  },
  watch: {
    '$route.name'() {
      if (this.isUserLogged) {
        // to trigger user activity
        this.$socket.disconnect()
        this.$socket.connect()
      }
    },
    'lessonIdForFeedback.length'() {
      this.showLessonEvaluationDialog()
    },
  },
  beforeMount() {
    // this.getUsersConnected()
    // eslint-disable-next-line no-unused-vars
    const { query } = this.$route

    // Load redirectUrl from localStorage if it exists
    // eslint-disable-next-line nuxt/no-env-in-hooks
    if (process.client) {
      this.$store.dispatch('user/loadRedirectUrl')

      // Check if we have a saved navigation state
      //      const navigationState = getNavigationState()
      //      if (navigationState && this.$store.getters['user/isUserLogged']) {
      // If user is logged in and we have a navigation state, apply it
      //        applyNavigationState(this.$router)
      //      }
    }

    // console.log(query, 'query')
    // if (query) {
    //   const utm = Object.keys(query).filter((key) =>
    //     this.$store.state.allowedUtmTags.includes(key)
    //   )

    //   if (utm.length) {
    //     const obj = {}

    //     utm.forEach((key) => {
    //       const _key = key
    //         .split('_')
    //         .map((str, index) =>
    //           index === 0 ? str : str.charAt(0).toUpperCase() + str.slice(1)
    //         )
    //         .join('')
    //       obj[_key] = query[key]
    //     })

    //     this.$store.commit('SET_UTM_TAGS', obj)
    //     window.localStorage.setItem('utm', JSON.stringify(obj))
    //   }
    // } else {
    //   const utmFromLocalStorage = window.localStorage.getItem('utm')

    //   if (utmFromLocalStorage) {
    //     this.$store.commit('SET_UTM_TAGS', JSON.parse(utmFromLocalStorage))
    //   }
    // }

    if (this.$dayjs) {
      this.$dayjs.locale(this.locale)
    }
  },
  mounted() {
    const el = document.body

    if (el && isTouchDevice()) {
      el.classList.add('is-touch-device')
    }

    this.showLessonEvaluationDialog()
  },
  methods: {
    closeBuzzDialog() {
      this.buzzItem = {}
      this.isShownBuzzDialog = false
    },
    getLessonFeedback() {
      this.$store
        .dispatch('lesson/getFeedbackItem', this.lessonIdForFeedback[0])
        .then((data) => {
          this.feedbackLessonData = data
          this.isShownLessonEvaluationDialog = true
        })
    },
    showLessonEvaluationDialog() {
      if (this.isStudent && this.lessonIdForFeedback.length) {
        this.getLessonFeedback()
      }
    },
    closeLessonEvaluationDialog() {
      this.isShownLessonEvaluationDialog = false
    },
    // getUsersConnected() {
    //   if (this.isUserLogged) {
    //     this.$socket.emit('get-users-connected')
    //   }
    // },
  },
  sockets: {
    connect() {
      this.$store.commit('socket/SET_IS_CONNECTED', true)

      // this.getUsersConnected()
    },
    disconnect() {
      this.$store.commit('socket/SET_IS_CONNECTED', false)

      // this.getUsersConnected()
    },
    reconnect() {
      this.$store.commit('socket/SET_IS_CONNECTED', true)

      // this.getUsersConnected()
    },
    // 'users-connected'(userIds) {
    //   this.$store.commit('socket/SET_CONNECTED_USER_IDS', userIds)
    // },
    'lesson:call'(data) {
      this.buzzItem = data
      this.isShownBuzzDialog = true
    },
    'lesson:init'(data) {
      this.$store.commit('lesson/UPDATE_LESSON', {
        lessonId: data.lesson,
        lessonStatus: 1,
      })
    },
    async 'message:received'(data) {
      await this.$store.dispatch('message/addMessage', data)

      if (this.$store.state.message.item?.id === data.threadId) {
        this.$socket.emit('message-read', data)
      }
    },
    'message:deleted'(data) {
      this.$store.commit('message/REMOVE_MESSAGE', data.message.id)
    },
    'message:read'(data) {
      this.$store.commit('message/UPDATE_READ_MESSAGE', data)
    },
  },
}
</script>
